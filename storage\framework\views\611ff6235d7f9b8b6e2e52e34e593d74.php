

<?php $__env->startSection('content'); ?>
    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>Contact Us</h1>
            <p>Get in touch for service questions or to schedule your can-to-curb service</p>
        </div>
    </section>

    <!-- Contact Information -->
    <section class="contact-info-section">
        <div class="container">
            <div class="contact-info-grid">
                <div class="contact-card-large">
                    <div class="contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <h3>Call Us</h3>
                    <p class="contact-detail">(405)634-6150</p>
                    <p class="contact-hours">We’ll grab the call if we can. If we are unavailable, we’ll call back!</p>
                </div>

                <div class="contact-card-large">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h3>Email Us</h3>
                    <p class="contact-detail"><EMAIL></p>
                    <p class="contact-hours">We respond within 24 hours</p>
                </div>

                <div class="contact-card-large">
                    <div class="contact-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h3>Service Area</h3>
                    <p class="contact-detail">Edmond, OK</p>
                    <!-- <p class="contact-hours">Expanding to new neighborhoods monthly</p> -->
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form -->
    <section class="contact-form-section">
        <div class="container">
            <div class="form-container">
                <h2>Send Us a Message</h2>
                <p>Have questions about our service? Need a custom quote? We're here to help!</p>

                <form id="contact-form" class="contact-form">
                    <?php echo csrf_field(); ?>
                    <div id="form-messages" class="alert" style="display: none;"></div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="contactFirstName">First Name *</label>
                            <input type="text" id="contactFirstName" name="first_name" required>
                        </div>
                        <div class="form-group">
                            <label for="contactLastName">Last Name *</label>
                            <input type="text" id="contactLastName" name="last_name" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="contactEmail">Email Address *</label>
                            <input type="email" id="contactEmail" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="contactPhone">Phone Number</label>
                            <input type="tel" id="contactPhone" name="phone">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="contactSubject">Subject *</label>
                        <select id="contactSubject" name="subject" required>
                            <option value="">Select a topic</option>
                            <option value="service-inquiry">Service Inquiry</option>
                            <option value="existing-service">Question About Existing Service</option>
                            <option value="billing">Billing Question</option>
                            <option value="service-area">Service Area Question</option>
                            <option value="custom-request">Custom Service Request</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="contactMessage">Message *</label>
                        <textarea id="contactMessage" name="message" rows="5" required
                            placeholder="Please provide details about your inquiry or service needs..."></textarea>
                    </div>

                    <div class="form-submit">
                        <button type="submit" class="btn btn-primary btn-large" id="submit-btn">
                            <span class="btn-text">Send Message</span>
                            <span class="btn-loading" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i> Sending...
                            </span>
                        </button>
                        <p class="form-note">* Required fields. We'll respond within 24 hours.</p>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="container">
            <h2 class="section-title text-center">Frequently Asked Questions</h2>
            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <h3>What areas do you serve?</h3>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>We proudly serve the entire Edmond area and surrounding neighborhoods. Contact us at
                            (405)634-6150 to check if we service your specific location.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <h3>How much does the service cost?</h3>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Our standard service is $49/month and includes 1 trash can and 1 recycle bin with weekly curbside
                            service. Extra charges may apply for long driveways or additional cans.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <h3>What if I have a gate or dogs?</h3>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>No problem! We handle gated properties and work around pets safely. Just provide us with access
                            codes, keys, or special instructions during booking, and we'll take care of the rest.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <h3>Can I pause my service?</h3>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>Absolutely! We offer flexible service options including temporary pauses for vacations, travel,
                            or any other needs. Just give us a call and we'll adjust your service accordingly.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <h3>When do you pick up the trash cans?</h3>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>We typically move your cans to the curb the evening before your scheduled pickup day and return
                            them to their designated location after collection. Exact timing may vary by route.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <h3>What makes you different from other services?</h3>
                        <span class="faq-icon">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>We're proud to be locally owned and operated in Edmond! This means personalized service, quick
                            response times, and a deep understanding of our community's needs. We treat every customer like
                            family.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
$(document).ready(function() {
    $('#contact-form').on('submit', function(e) {
        e.preventDefault();

        const form = $(this);
        const submitBtn = $('#submit-btn');
        const btnText = submitBtn.find('.btn-text');
        const btnLoading = submitBtn.find('.btn-loading');
        const messagesDiv = $('#form-messages');

        // Show loading state
        submitBtn.prop('disabled', true);
        btnText.hide();
        btnLoading.show();
        messagesDiv.hide();

        // Prepare form data
        const formData = new FormData(this);

        $.ajax({
            url: '<?php echo e(route("contact.submit")); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    messagesDiv.removeClass('alert-danger').addClass('alert-success')
                        .html('<i class="fas fa-check-circle"></i> ' + response.message).show();
                    form[0].reset();

                    // Show success message in guest toast as well
                    if (typeof showGuestToast === 'function') {
                        showGuestToast(response.message, 'success');
                    }
                } else {
                    messagesDiv.removeClass('alert-success').addClass('alert-danger')
                        .html('<i class="fas fa-exclamation-circle"></i> ' + response.message).show();
                }
            },
            error: function(xhr) {
                let errorMessage = 'Something went wrong. Please try again.';
                let toastMessage = errorMessage; // Simple message for toast

                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    errorMessage = '<ul class="mb-0">';
                    let firstError = '';
                    $.each(errors, function(field, messages) {
                        $.each(messages, function(index, message) {
                            errorMessage += '<li>' + message + '</li>';
                            if (!firstError) firstError = message; // Get first error for toast
                        });
                    });
                    errorMessage += '</ul>';
                    toastMessage = firstError || 'Please check the form for errors.';
                }

                messagesDiv.removeClass('alert-success').addClass('alert-danger')
                    .html('<i class="fas fa-exclamation-circle"></i> ' + errorMessage).show();

                // Show error message in guest toast as well
                if (typeof showGuestToast === 'function') {
                    showGuestToast(toastMessage, 'error');
                }
            },
            complete: function() {
                // Reset button state
                submitBtn.prop('disabled', false);
                btnText.show();
                btnLoading.hide();

                // Scroll to messages
                $('html, body').animate({
                    scrollTop: messagesDiv.offset().top - 100
                }, 500);
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/guest/contact.blade.php ENDPATH**/ ?>