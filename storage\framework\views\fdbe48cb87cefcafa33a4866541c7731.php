<?php $__env->startSection('content'); ?>
    <?php echo $__env->make('layouts.navbars.auth.topnav', ['title' => 'Contact Message Details'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <div class="container-fluid py-4">
        <!-- Contact Header Card -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-3">
                        <h4 class="mb-2"><?php echo e($contact->full_name); ?></h4>
                        <div class="mb-2">
                            <span class="text-muted"><?php echo e($contact->email); ?></span>
                            <?php if($contact->phone): ?>
                                <span class="text-muted"> | <?php echo e($contact->phone); ?></span>
                            <?php endif; ?>
                        </div>
                        <div class="mb-3">
                            <?php if($contact->status == 1): ?>
                                <span class="badge bg-success">
                                    <i class="fas fa-eye"></i> Viewed
                                </span>
                            <?php else: ?>
                                <span class="badge bg-warning">
                                    <i class="fas fa-clock"></i> Pending
                                </span>
                            <?php endif; ?>
                        </div>

                        <div class="d-flex justify-content-center gap-2">
                            <a href="<?php echo e(route('contact.index')); ?>" class="btn btn-sm" style="background-color: #8392ab; border-color: #8392ab; color: white;">
                                <i class="fas fa-arrow-left me-1"></i> Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Details Cards -->
        <div class="row">
            <!-- Message Content Card -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header pb-0">
                        <h6 class="mb-0" style="color: #67748e;">Message Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>Subject:</strong>
                            </div>
                            <div class="col-sm-9">
                                <span class="badge bg-info"><?php echo e(ucwords(str_replace('-', ' ', $contact->subject))); ?></span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>Message:</strong>
                            </div>
                            <div class="col-sm-9">
                                <div class="p-3 bg-light rounded">
                                    <?php echo nl2br(e($contact->message)); ?>

                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-3">
                                <strong>Received:</strong>
                            </div>
                            <div class="col-sm-9">
                                <?php echo e($contact->created_at->format('l, F j, Y \a\t g:i A')); ?>

                                <small class="text-muted">(<?php echo e($contact->created_at->diffForHumans()); ?>)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information Card -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header pb-0">
                        <h6 class="mb-0" style="color: #67748e;">Contact Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Full Name:</strong><br>
                            <span><?php echo e($contact->full_name); ?></span>
                        </div>

                        <div class="mb-3">
                            <strong>Email Address:</strong><br>
                            <a href="mailto:<?php echo e($contact->email); ?>" class="text-decoration-none">
                                <i class="fas fa-envelope me-1"></i><?php echo e($contact->email); ?>

                            </a>
                        </div>

                        <?php if($contact->phone): ?>
                        <div class="mb-3">
                            <strong>Phone Number:</strong><br>
                            <a href="tel:<?php echo e($contact->phone); ?>" class="text-decoration-none">
                                <i class="fas fa-phone me-1"></i><?php echo e($contact->phone); ?>

                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/admin/contact/show.blade.php ENDPATH**/ ?>